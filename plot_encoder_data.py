#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import argparse
import os

def plot_encoder_data(input_file, output_dir=None):
    """
    读取编码器数据文件并绘制曲线
    
    Args:
        input_file: 编码器数据文件路径
        output_dir: 输出图表的目录路径，如果不指定则不保存图片
    """
    # 检查文件是否存在
    if not os.path.exists(input_file):
        print(f"错误：文件 {input_file} 不存在")
        return
    
    # 读取CSV数据
    try:
        data = pd.read_csv(input_file)
        print(f"已成功读取数据文件，共 {len(data)} 条记录")
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 将时间戳转换为相对时间（秒）
    if 'timestamp' in data.columns:
        data['relative_time'] = (data['timestamp'] - data['timestamp'].iloc[0]) / 1000.0
    
    # 创建多个子图
    fig, axes = plt.subplots(3, 1, figsize=(12, 15), sharex=True)
    
    # 绘制位移数据
    ax = axes[0]
    time_array = data['relative_time'].to_numpy()
    
    if 'linear_displacement' in data.columns:
        ax.plot(time_array, data['linear_displacement'].to_numpy(), 'b-', label='Linear Displacement')
    if 'angular_displacement' in data.columns:
        ax.plot(time_array, data['angular_displacement'].to_numpy(), 'r-', label='Angular Displacement')
    
    ax.set_title('Displacement Data')
    ax.set_ylabel('Displacement (m / rad)')
    ax.legend(loc='upper right')
    ax.grid(True)
    
    # 绘制速度数据
    ax = axes[1]
    
    if 'linear_velocity' in data.columns:
        ax.plot(time_array, data['linear_velocity'].to_numpy(), 'b-', label='Linear Velocity')
    if 'angular_velocity' in data.columns:
        ax.plot(time_array, data['angular_velocity'].to_numpy(), 'r-', label='Angular Velocity')
    
    ax.set_title('Velocity Data')
    ax.set_ylabel('Velocity (m/s / rad/s)')
    ax.legend(loc='upper right')
    ax.grid(True)
    
    # 绘制累积位移
    ax = axes[2]
    
    if 'linear_displacement' in data.columns:
        cumulative_linear = np.cumsum(data['linear_displacement'].to_numpy())
        ax.plot(time_array, cumulative_linear, 'b-', label='Cumulative Linear Displacement')
    
    if 'angular_displacement' in data.columns:
        cumulative_angular = np.cumsum(data['angular_displacement'].to_numpy())
        ax.plot(time_array, cumulative_angular, 'r-', label='Cumulative Angular Displacement')
    
    ax.set_title('Cumulative Displacement Data')
    ax.set_xlabel('Time (seconds)')
    ax.set_ylabel('Cumulative Displacement (m / rad)')
    ax.legend(loc='upper right')
    ax.grid(True)
    
    # 设置子图之间的间距
    plt.tight_layout()
    
    # 保存图片（如果指定了输出目录）
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        output_file = os.path.join(output_dir, 'encoder_data_plot.png')
        plt.savefig(output_file, dpi=300)
        print(f"已保存图表到: {output_file}")
    
    # 显示图表
    plt.show()

def analyze_stuck_detection(input_file):
    """
    分析脱困检测数据
    
    Args:
        input_file: 编码器数据文件路径
    """
    try:
        data = pd.read_csv(input_file)
        print(f"\n=== 脱困检测数据分析 ===")
        print(f"数据记录总数: {len(data)}")
        
        if len(data) == 0:
            print("没有数据可分析")
            return
        
        # 时间范围
        if 'timestamp' in data.columns:
            time_span = (data['timestamp'].iloc[-1] - data['timestamp'].iloc[0]) / 1000.0
            print(f"数据时间跨度: {time_span:.1f} 秒")
        
        # 位移统计
        if 'linear_displacement' in data.columns:
            total_linear = data['linear_displacement'].sum()
            avg_linear = data['linear_displacement'].mean()
            max_linear = data['linear_displacement'].max()
            print(f"总线性位移: {total_linear:.3f} m")
            print(f"平均线性位移: {avg_linear:.6f} m")
            print(f"最大线性位移: {max_linear:.6f} m")
        
        if 'angular_displacement' in data.columns:
            total_angular = data['angular_displacement'].sum()
            avg_angular = data['angular_displacement'].mean()
            max_angular = data['angular_displacement'].max()
            print(f"总角度位移: {total_angular:.3f} rad ({total_angular * 180 / np.pi:.1f} 度)")
            print(f"平均角度位移: {avg_angular:.6f} rad")
            print(f"最大角度位移: {max_angular:.6f} rad")
        
        # 速度统计
        if 'linear_velocity' in data.columns:
            avg_linear_vel = data['linear_velocity'].mean()
            max_linear_vel = data['linear_velocity'].max()
            print(f"平均线性速度: {avg_linear_vel:.3f} m/s")
            print(f"最大线性速度: {max_linear_vel:.3f} m/s")
        
        if 'angular_velocity' in data.columns:
            avg_angular_vel = data['angular_velocity'].mean()
            max_angular_vel = data['angular_velocity'].max()
            print(f"平均角速度: {avg_angular_vel:.3f} rad/s ({avg_angular_vel * 180 / np.pi:.1f} 度/s)")
            print(f"最大角速度: {max_angular_vel:.3f} rad/s ({max_angular_vel * 180 / np.pi:.1f} 度/s)")
        
        # 模拟时间窗口检测
        print(f"\n=== 时间窗口分析 ===")
        windows = [30, 120, 300]  # 30秒, 2分钟, 5分钟
        thresholds_displacement = [0.1, 0.3, 0.5]
        thresholds_rotation = [0.1, 0.3, 0.5]
        
        for i, window_sec in enumerate(windows):
            if time_span >= window_sec:
                window_data = data.tail(int(len(data) * window_sec / time_span))
                
                if 'linear_displacement' in window_data.columns:
                    window_linear = window_data['linear_displacement'].sum()
                else:
                    window_linear = 0
                
                if 'angular_displacement' in window_data.columns:
                    window_angular = abs(window_data['angular_displacement'].sum())
                else:
                    window_angular = 0
                
                is_stuck = (window_linear < thresholds_displacement[i]) and (window_angular < thresholds_rotation[i])
                status = "被困" if is_stuck else "正常"
                
                print(f"{window_sec}秒窗口: 位移={window_linear:.3f}m, 旋转={window_angular:.3f}rad, 状态={status}")
        
    except Exception as e:
        print(f"分析数据时出错: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='绘制编码器数据曲线和分析脱困检测')
    parser.add_argument('input_file', help='编码器数据文件路径')
    parser.add_argument('--output_dir', help='输出图表的目录路径（可选）')
    parser.add_argument('--analyze', action='store_true', help='执行脱困检测分析')
    
    args = parser.parse_args()
    
    if args.analyze:
        analyze_stuck_detection(args.input_file)
    
    plot_encoder_data(args.input_file, args.output_dir)
