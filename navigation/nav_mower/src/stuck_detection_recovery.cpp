#include "stuck_detection_recovery.hpp"

#include "utils/time.hpp"

#include <algorithm>
#include <cmath>
#include <iomanip>
#include <sstream>

namespace fescue_iox
{

StuckDetectionRecovery::StuckDetectionRecovery(const StuckRecoveryParam &param)
    : param_(param)
    , current_linear_speed_(param.initial_linear_speed)
    , current_angular_speed_(param.initial_angular_speed)
    , recovery_start_time_(0)
    , last_action_time_(0)
    , recovery_cycle_count_(0)
{
    LOG_INFO("[StuckDetectionRecovery] 初始化脱困恢复系统");
}

StuckDetectionRecovery::~StuckDetectionRecovery()
{
    Shutdown();
    LOG_INFO("[StuckDetectionRecovery] 脱困恢复系统已关闭");
}

void StuckDetectionRecovery::Initialize()
{
    LOG_INFO("[StuckDetectionRecovery] 启动脱困检测和恢复线程");

    // 初始化数据记录
    if (param_.enable_data_logging)
    {
        InitializeDataLogging();
    }

    // 启动检测线程
    detection_running_.store(true);
    detection_thread_ = std::thread(&StuckDetectionRecovery::DetectionThread, this);

    // 启动恢复线程
    recovery_running_.store(true);
    recovery_thread_ = std::thread(&StuckDetectionRecovery::RecoveryThread, this);
}

void StuckDetectionRecovery::Shutdown()
{
    LOG_INFO("[StuckDetectionRecovery] 关闭脱困检测和恢复线程");

    // 停止线程
    detection_running_.store(false);
    recovery_running_.store(false);

    if (detection_thread_.joinable())
    {
        detection_thread_.join();
    }

    if (recovery_thread_.joinable())
    {
        recovery_thread_.join();
    }

    // 关闭数据记录
    CloseDataLogging();
}

void StuckDetectionRecovery::SetImuData(const ImuData &imu_data)
{
    std::lock_guard<std::mutex> lock(imu_mutex_);
    latest_imu_data_ = imu_data;
    imu_data_valid_ = true;

    // 处理IMU数据
    ProcessImuData(imu_data);
}

void StuckDetectionRecovery::SetMotorSpeedData(const MotorSpeedData &motor_speed_data)
{
    std::lock_guard<std::mutex> lock(motor_mutex_);
    latest_motor_data_ = motor_speed_data;
    motor_data_valid_ = true;

    // 处理电机数据
    ProcessMotorData(motor_speed_data);
}

bool StuckDetectionRecovery::IsStuck()
{
    return is_stuck_.load();
}

bool StuckDetectionRecovery::StartRecovery()
{
    if (recovery_active_.load())
    {
        LOG_WARN("[StuckDetectionRecovery] 恢复已在进行中");
        return false;
    }

    LOG_INFO("[StuckDetectionRecovery] 开始脱困恢复");
    recovery_active_.store(true);
    recovery_start_time_ = GetCurrentTimestamp();
    last_action_time_ = recovery_start_time_;
    recovery_cycle_count_ = 0;

    // 重置速度为初始值
    current_linear_speed_ = param_.initial_linear_speed;
    current_angular_speed_ = param_.initial_angular_speed;

    // 记录恢复开始位置
    ResetMovementTracking();

    return true;
}

void StuckDetectionRecovery::StopRecovery()
{
    if (!recovery_active_.load())
    {
        return;
    }

    LOG_INFO("[StuckDetectionRecovery] 停止脱困恢复");
    recovery_active_.store(false);
    current_recovery_mode_ = RecoveryMode::NONE;

    // 停止运动
    PublishVelocity(0.0f, 0.0f);
}

bool StuckDetectionRecovery::IsRecoveryActive() const
{
    return recovery_active_.load();
}

void StuckDetectionRecovery::SetVelocityPublisher(std::shared_ptr<VelocityPublisher> vel_publisher)
{
    vel_publisher_ = vel_publisher;
}

void StuckDetectionRecovery::SetParam(const StuckRecoveryParam &param)
{
    param_ = param;
}

StuckRecoveryParam StuckDetectionRecovery::GetParam() const
{
    return param_;
}

RecoveryMode StuckDetectionRecovery::GetCurrentRecoveryMode() const
{
    return current_recovery_mode_;
}

std::vector<WindowDetectionResult> StuckDetectionRecovery::GetDetectionResults() const
{
    return detection_results_;
}

void StuckDetectionRecovery::DetectionThread()
{
    LOG_INFO("[StuckDetectionRecovery] 检测线程已启动");

    while (detection_running_.load())
    {
        // 更新运动数据
        UpdateMovementData();

        // 执行多窗口检测
        bool stuck_detected = IsStuckInMultipleWindows();

        // 更新被困状态
        bool previous_stuck = is_stuck_.exchange(stuck_detected);

        if (stuck_detected && !previous_stuck)
        {
            LOG_WARN("[StuckDetectionRecovery] 检测到被困状态");
        }
        else if (!stuck_detected && previous_stuck)
        {
            LOG_INFO("[StuckDetectionRecovery] 被困状态已解除");
        }

        // 控制检测频率 (10Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    LOG_INFO("[StuckDetectionRecovery] 检测线程已退出");
}

void StuckDetectionRecovery::RecoveryThread()
{
    LOG_INFO("[StuckDetectionRecovery] 恢复线程已启动");

    while (recovery_running_.load())
    {
        if (recovery_active_.load())
        {
            uint64_t current_time = GetCurrentTimestamp();

            // 检查是否超过最大恢复时间
            if (current_time - recovery_start_time_ > param_.max_recovery_duration_ms)
            {
                LOG_WARN("[StuckDetectionRecovery] 恢复超时，停止恢复");
                StopRecovery();
                continue;
            }

            // 检查恢复期间是否有运动
            if (HasMovementDuringRecovery())
            {
                LOG_INFO("[StuckDetectionRecovery] 检测到运动，恢复成功");
                StopRecovery();
                continue;
            }

            // 执行恢复动作
            if (current_time - last_action_time_ > param_.recovery_action_duration_ms)
            {
                // 切换到下一个恢复模式
                current_recovery_mode_ = GetNextRecoveryMode();

                // 渐进式调整速度
                ProgressiveSpeedAdjustment();

                // 执行恢复动作
                ExecuteRecoveryAction(current_recovery_mode_, current_linear_speed_, current_angular_speed_);

                last_action_time_ = current_time;
                recovery_cycle_count_++;

                LOG_INFO("[StuckDetectionRecovery] 执行恢复动作: 模式={}, 线速度={:.2f}, 角速度={:.2f}",
                         static_cast<int>(current_recovery_mode_), current_linear_speed_, current_angular_speed_);
            }
        }

        // 控制恢复频率 (20Hz)
        std::this_thread::sleep_for(std::chrono::milliseconds(20));
    }

    LOG_INFO("[StuckDetectionRecovery] 恢复线程已退出");
}

void StuckDetectionRecovery::ProcessImuData(const ImuData &imu_data)
{
    // 仿照nav_cross_region的IMU处理方式
    auto current_time = std::chrono::steady_clock::now();
    float dt = 0.0f;

    if (!is_first_imu_)
    {
        dt = std::chrono::duration<float>(current_time - last_imu_time_).count();
    }
    else
    {
        is_first_imu_ = false;
        LOG_INFO("[StuckDetectionRecovery] 忽略第一次IMU数据");
        last_imu_time_ = current_time;
        last_imu_timestamp_ = imu_data.system_timestamp;
        return;
    }

    last_imu_time_ = current_time;
    last_imu_timestamp_ = imu_data.system_timestamp;

    // IMU零偏校准
    if (!is_bias_calibrated_)
    {
        // 忽略第一秒数据进行校准
        if (dt > 0.0f && calibration_samples_.size() < CALIBRATION_SAMPLES)
        {
            calibration_samples_.push_back(imu_data.angular_velocity_z);
        }

        if (calibration_samples_.size() >= CALIBRATION_SAMPLES)
        {
            // 计算平均值作为零偏
            float sum = 0.0f;
            for (float sample : calibration_samples_)
            {
                sum += sample;
            }
            bias_z_ = sum / calibration_samples_.size();
            is_bias_calibrated_ = true;

            LOG_INFO("[StuckDetectionRecovery] IMU零偏校准完成: bias_z = {:.4f}", bias_z_);
        }
        return;
    }

    // 应用零偏校正和阈值滤波
    float angular_velocity_z = imu_data.angular_velocity_z - bias_z_;
    if (std::abs(angular_velocity_z) < bias_threshold_)
    {
        angular_velocity_z = 0.0f;
    }

    // 累积角度变化
    accumulated_rotation_ += angular_velocity_z * dt;

    // 更新当前运动数据
    std::lock_guard<std::mutex> lock(movement_mutex_);
    current_movement_.angular_velocity = angular_velocity_z;
    current_movement_.angular_displacement += angular_velocity_z * dt;
    current_movement_.timestamp = imu_data.system_timestamp;
}

void StuckDetectionRecovery::ProcessMotorData(const MotorSpeedData &motor_data)
{
    // 计算实际线性和角速度
    float w_left = motor_data.motor_speed_left * 2 * M_PI / 60.0f; // RPM转rad/s
    float w_right = motor_data.motor_speed_right * 2 * M_PI / 60.0f;

    float v_left = w_left * param_.wheel_radius;
    float v_right = w_right * param_.wheel_radius;

    float linear_velocity = (v_right + v_left) / 2.0f;
    float angular_velocity = (v_right - v_left) / param_.wheel_base;

    // 计算时间差
    static uint64_t last_motor_timestamp = 0;
    float dt = 0.0f;
    if (last_motor_timestamp > 0)
    {
        dt = (motor_data.system_timestamp - last_motor_timestamp) / 1000.0f; // ms转s
    }
    last_motor_timestamp = motor_data.system_timestamp;

    if (dt > 0.0f && dt < 1.0f) // 合理的时间间隔
    {
        // 累积线性位移
        accumulated_displacement_ += std::abs(linear_velocity) * dt;

        // 更新当前运动数据
        std::lock_guard<std::mutex> lock(movement_mutex_);
        current_movement_.linear_velocity = linear_velocity;
        current_movement_.linear_displacement += std::abs(linear_velocity) * dt;
        current_movement_.timestamp = motor_data.system_timestamp;
    }
}

void StuckDetectionRecovery::UpdateMovementData()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);

    uint64_t current_time = GetCurrentTimestamp();

    // 如果有新的运动数据，添加到历史记录
    if (current_movement_.timestamp > 0)
    {
        MovementData data = current_movement_;
        data.timestamp = current_time;

        movement_history_.push_back(data);

        // 记录数据
        if (param_.enable_data_logging)
        {
            LogData(data);
        }

        // 重置当前运动数据
        current_movement_ = MovementData();
    }

    // 清理超过5分钟的历史数据
    uint64_t cutoff_time = current_time - 5 * 60 * 1000; // 5分钟
    while (!movement_history_.empty() && movement_history_.front().timestamp < cutoff_time)
    {
        movement_history_.pop_front();
    }
}

WindowDetectionResult StuckDetectionRecovery::CheckWindow(uint64_t window_duration_ms,
                                                          float min_displacement,
                                                          float min_rotation)
{
    WindowDetectionResult result;
    result.window_duration_ms = window_duration_ms;

    uint64_t current_time = GetCurrentTimestamp();
    uint64_t window_start_time = current_time - window_duration_ms;

    std::lock_guard<std::mutex> lock(movement_mutex_);

    // 计算窗口内的总位移和旋转
    for (const auto &data : movement_history_)
    {
        if (data.timestamp >= window_start_time)
        {
            result.total_displacement += data.linear_displacement;
            result.total_rotation += std::abs(data.angular_displacement);
        }
    }

    // 判断是否被困
    result.is_stuck = (result.total_displacement < min_displacement) &&
                      (result.total_rotation < min_rotation);

    return result;
}

bool StuckDetectionRecovery::IsStuckInMultipleWindows()
{
    // 检查三个时间窗口
    WindowDetectionResult result_30s = CheckWindow(30 * 1000,
                                                   param_.min_displacement_threshold_30s,
                                                   param_.min_rotation_threshold_30s);

    WindowDetectionResult result_2min = CheckWindow(2 * 60 * 1000,
                                                    param_.min_displacement_threshold_2min,
                                                    param_.min_rotation_threshold_2min);

    WindowDetectionResult result_5min = CheckWindow(5 * 60 * 1000,
                                                    param_.min_displacement_threshold_5min,
                                                    param_.min_rotation_threshold_5min);

    // 更新检测结果
    detection_results_.clear();
    detection_results_.push_back(result_30s);
    detection_results_.push_back(result_2min);
    detection_results_.push_back(result_5min);

    // 至少两个窗口检测到被困才确认
    int stuck_count = 0;
    if (result_30s.is_stuck)
        stuck_count++;
    if (result_2min.is_stuck)
        stuck_count++;
    if (result_5min.is_stuck)
        stuck_count++;

    bool is_stuck = stuck_count >= 2;

    if (is_stuck)
    {
        LOG_WARN("[StuckDetectionRecovery] 多窗口检测被困: 30s({:.3f}m,{:.3f}rad), 2min({:.3f}m,{:.3f}rad), 5min({:.3f}m,{:.3f}rad)",
                 result_30s.total_displacement, result_30s.total_rotation,
                 result_2min.total_displacement, result_2min.total_rotation,
                 result_5min.total_displacement, result_5min.total_rotation);
    }

    return is_stuck;
}

void StuckDetectionRecovery::ExecuteRecoveryAction(RecoveryMode mode, float linear_speed, float angular_speed)
{
    switch (mode)
    {
    case RecoveryMode::ROTATE_LEFT:
        PublishVelocity(0.0f, angular_speed);
        break;

    case RecoveryMode::ROTATE_RIGHT:
        PublishVelocity(0.0f, -angular_speed);
        break;

    case RecoveryMode::FORWARD:
        PublishVelocity(linear_speed, 0.0f);
        break;

    case RecoveryMode::BACKWARD:
        PublishVelocity(-linear_speed, 0.0f);
        break;

    case RecoveryMode::SINGLE_WHEEL_LEFT:
        // 左轮高速旋转，右轮慢速
        PublishVelocity(linear_speed * 0.3f, angular_speed);
        break;

    case RecoveryMode::SINGLE_WHEEL_RIGHT:
        // 右轮高速旋转，左轮慢速
        PublishVelocity(linear_speed * 0.3f, -angular_speed);
        break;

    case RecoveryMode::ALTERNATING_PUSH:
        // 交替前进后退
        if (recovery_cycle_count_ % 2 == 0)
        {
            PublishVelocity(linear_speed, 0.0f);
        }
        else
        {
            PublishVelocity(-linear_speed, 0.0f);
        }
        break;

    default:
        PublishVelocity(0.0f, 0.0f);
        break;
    }
}

RecoveryMode StuckDetectionRecovery::GetNextRecoveryMode()
{
    // 循环使用不同的恢复模式
    static const std::vector<RecoveryMode> recovery_modes = {
        RecoveryMode::ROTATE_LEFT,
        RecoveryMode::ROTATE_RIGHT,
        RecoveryMode::BACKWARD,
        RecoveryMode::FORWARD,
        RecoveryMode::SINGLE_WHEEL_LEFT,
        RecoveryMode::SINGLE_WHEEL_RIGHT,
        RecoveryMode::ALTERNATING_PUSH};

    int mode_index = recovery_cycle_count_ % recovery_modes.size();
    return recovery_modes[mode_index];
}

void StuckDetectionRecovery::ProgressiveSpeedAdjustment()
{
    // 每几个周期增加速度
    if (recovery_cycle_count_ > 0 && recovery_cycle_count_ % 3 == 0)
    {
        current_linear_speed_ = std::min(current_linear_speed_ + param_.speed_increment,
                                         param_.max_linear_speed);
        current_angular_speed_ = std::min(current_angular_speed_ + param_.speed_increment,
                                          param_.max_angular_speed);

        LOG_INFO("[StuckDetectionRecovery] 速度递增: 线速度={:.2f}, 角速度={:.2f}",
                 current_linear_speed_, current_angular_speed_);
    }
}

bool StuckDetectionRecovery::HasMovementDuringRecovery()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);

    // 计算恢复开始以来的总位移
    float total_displacement = 0.0f;
    uint64_t recovery_start = recovery_start_time_;

    for (const auto &data : movement_history_)
    {
        if (data.timestamp >= recovery_start)
        {
            total_displacement += data.linear_displacement;
        }
    }

    return total_displacement > recovery_displacement_threshold_;
}

void StuckDetectionRecovery::ResetMovementTracking()
{
    std::lock_guard<std::mutex> lock(movement_mutex_);
    recovery_start_position_ = MovementData();
    recovery_start_position_.timestamp = GetCurrentTimestamp();
}

void StuckDetectionRecovery::InitializeDataLogging()
{
    try
    {
        data_log_file_.open(param_.log_file_path, std::ios::out | std::ios::trunc);
        if (data_log_file_.is_open())
        {
            // 写入CSV头部
            data_log_file_ << "timestamp,linear_displacement,angular_displacement,linear_velocity,angular_velocity\n";
            data_logging_initialized_ = true;
            LOG_INFO("[StuckDetectionRecovery] 数据记录已初始化: {}", param_.log_file_path);
        }
        else
        {
            LOG_ERROR("[StuckDetectionRecovery] 无法打开数据记录文件: {}", param_.log_file_path);
        }
    }
    catch (const std::exception &e)
    {
        LOG_ERROR("[StuckDetectionRecovery] 数据记录初始化失败: {}", e.what());
    }
}

void StuckDetectionRecovery::LogData(const MovementData &data)
{
    if (data_logging_initialized_ && data_log_file_.is_open())
    {
        data_log_file_ << data.timestamp << ","
                       << data.linear_displacement << ","
                       << data.angular_displacement << ","
                       << data.linear_velocity << ","
                       << data.angular_velocity << "\n";
        data_log_file_.flush();
    }
}

void StuckDetectionRecovery::CloseDataLogging()
{
    if (data_log_file_.is_open())
    {
        data_log_file_.close();
        LOG_INFO("[StuckDetectionRecovery] 数据记录已关闭");
    }
    data_logging_initialized_ = false;
}

uint64_t StuckDetectionRecovery::GetCurrentTimestamp() const
{
    return GetTimestampMs();
}

float StuckDetectionRecovery::CalculateDisplacement(float linear_vel, float dt)
{
    return std::abs(linear_vel) * dt;
}

float StuckDetectionRecovery::CalculateRotation(float angular_vel, float dt)
{
    return std::abs(angular_vel) * dt;
}

void StuckDetectionRecovery::PublishVelocity(float linear, float angular)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular);
    }
}

} // namespace fescue_iox
